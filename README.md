# dann-spawn

This resource is a basic spawn select menu for FiveM. This allows users to select a location with FiveM that they wish to spawn at and will spawn them there. This resources also allows for ease of use as it translates real in game positions to the correct position on the user infacter

### Features
• Enhanced UX & Stunning UI Interactive, draggable, and zoomable map

• Responsive design High performance

• Easy and fully configurable

• Custom name positions

• Support for custom locations like last location

• Predefined popular spawn positions

• Easy translation Smooth camera transitions

• Beautiful in/out and hover animations

## Config

- Locations - Set of default locations for spawn UI
- LastLocation - Toggle Last Location

### [Preview](https://www.youtube.com/watch?v=3BP-mVIOzNA)
