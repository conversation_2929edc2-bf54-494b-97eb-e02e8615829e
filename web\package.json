{"name": "web", "homepage": "web/build", "private": true, "version": "0.1.0", "scripts": {"start": "vite", "start:game": "vite build --watch", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/utilities": "^3.2.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/pro-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@mantine/core": "^6.0.7", "@mantine/form": "^6.0.6", "@mantine/hooks": "^6.0.4", "@mantine/modals": "^6.0.4", "@mantine/tiptap": "^6.0.7", "@mantine/utils": "^6.0.15", "@mui/material": "^5.14.2", "@tabler/icons-react": "^2.11.0", "@tiptap/core": "^2.0.2", "@tiptap/extension-color": "^2.0.2", "@tiptap/extension-highlight": "^2.0.2", "@tiptap/extension-image": "^2.0.3", "@tiptap/extension-link": "^2.0.2", "@tiptap/extension-subscript": "^2.0.2", "@tiptap/extension-superscript": "^2.0.2", "@tiptap/extension-text-align": "^2.0.2", "@tiptap/extension-text-style": "^2.0.2", "@tiptap/extension-underline": "^2.0.2", "@tiptap/pm": "^2.0.2", "@tiptap/react": "^2.0.2", "@tiptap/starter-kit": "^2.0.2", "build": "^0.1.4", "dayjs": "^1.11.7", "immer": "^9.0.21", "leaflet": "^1.9.3", "leaflet.markercluster": "^1.5.3", "mantine-datatable": "^2.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.8.0", "react-leaflet": "^4.2.1", "zustand": "^4.3.7"}, "devDependencies": {"@types/leaflet": "^1.9.3", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "i": "^0.3.7", "react-router-dom": "^6.9.0", "typescript": "^4.9.3", "vite": "^4.2.0"}}